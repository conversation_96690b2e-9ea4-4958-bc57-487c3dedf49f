{"name": "dev-productivity-suite", "version": "0.1.0", "description": "Dev Productivity Suite", "private": true, "main": "main.js", "type": "module", "dependencies": {"electron-is-dev": "^2.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "three": "^0.150.1", "xterm": "^5.1.0", "xterm-addon-fit": "^0.7.0"}, "devDependencies": {"concurrently": "^7.6.0", "cross-env": "^7.0.3", "electron": "^37.1.0", "electron-builder": "^26.0.12", "react-scripts": "^5.0.1", "wait-on": "^7.0.1"}, "scripts": {"start": "npx react-scripts start", "build": "npx react-scripts build", "test": "npx react-scripts test", "eject": "npx react-scripts eject", "electron:dev": "concurrently \"npx react-scripts start\" \"wait-on http://localhost:3000 && electron .\"", "electron:build": "npm run build && electron-builder", "electron:prod": "npm run build && electron .", "electron:package": "npm run build && electron-builder", "electron:clean": "<PERSON><PERSON>f build dist", "electron:run": "electron .", "electron:start": "concurrently \"cross-env BROWSER=none npx react-scripts start\" \"wait-on http://localhost:3000 && electron . --no-devtools\""}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}