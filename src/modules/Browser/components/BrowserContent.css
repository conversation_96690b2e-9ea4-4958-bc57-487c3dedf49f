.browser-content {
  flex: 1;
  position: relative;
  background-color: #ffffff;
  overflow: hidden;
}

.webview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  background-color: #ffffff;
  transition: opacity 0.3s ease;
}

.webview.loading {
  opacity: 0.8;
}

.webview.hidden {
  display: none;
}

/* Loading State */
.loading-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  z-index: 10;
}

.loading-spinner-large {
  width: 48px;
  height: 48px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: #6c757d;
  font-weight: 500;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #007bff;
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
    margin-left: 0%;
  }
  50% {
    width: 75%;
    margin-left: 0%;
  }
  100% {
    width: 0%;
    margin-left: 100%;
  }
}

/* Error State */
.error-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 40px 20px;
  text-align: center;
  z-index: 10;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 24px;
}

.error-state h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #495057;
}

.error-message {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #6c757d;
  max-width: 500px;
  line-height: 1.5;
}

.error-suggestions {
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  max-width: 400px;
  text-align: left;
}

.error-suggestions h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.error-suggestions ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.error-suggestions li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.retry-button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: #0056b3;
}

.retry-button:active {
  background-color: #004085;
  transform: scale(0.98);
}

/* Responsive design */
@media (max-width: 768px) {
  .loading-state {
    padding: 20px;
  }
  
  .loading-spinner-large {
    width: 40px;
    height: 40px;
    border-width: 3px;
  }
  
  .loading-state p {
    font-size: 14px;
  }
  
  .loading-progress {
    width: 150px;
  }
  
  .error-state {
    padding: 20px;
  }
  
  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .error-state h3 {
    font-size: 20px;
    margin-bottom: 8px;
  }
  
  .error-message {
    font-size: 14px;
    margin-bottom: 24px;
  }
  
  .error-suggestions {
    padding: 16px;
    margin-bottom: 24px;
  }
  
  .retry-button {
    padding: 10px 20px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .error-suggestions {
    max-width: none;
    width: 100%;
  }
  
  .loading-progress {
    width: 120px;
  }
}

/* Security indicators */
.webview[src^="https://"] {
  border-left: 3px solid #28a745;
}

.webview[src^="http://"] {
  border-left: 3px solid #ffc107;
}

/* Focus states for accessibility */
.webview:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}
