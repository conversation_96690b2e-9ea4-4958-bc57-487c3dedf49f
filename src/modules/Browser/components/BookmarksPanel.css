.bookmarks-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-top: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 400px;
  display: flex;
  flex-direction: column;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bookmarks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.bookmarks-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.close-button {
  background: none;
  border: none;
  font-size: 18px;
  font-weight: bold;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #e9ecef;
  color: #495057;
}

.bookmarks-search {
  position: relative;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  background-color: #f8f9fa;
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 28px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

.bookmarks-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.empty-bookmarks {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.empty-state {
  max-width: 300px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.bookmark-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.bookmark-item:hover {
  background-color: #f8f9fa;
}

.bookmark-item:last-child {
  border-bottom: none;
}

.bookmark-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.bookmark-favicon {
  font-size: 16px;
  flex-shrink: 0;
}

.bookmark-info {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  font-size: 14px;
  font-weight: 500;
  color: #212529;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-url {
  font-size: 12px;
  color: #6c757d;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-bookmark {
  background: none;
  border: none;
  font-size: 14px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  opacity: 0;
  flex-shrink: 0;
}

.bookmark-item:hover .remove-bookmark {
  opacity: 1;
}

.remove-bookmark:hover {
  background-color: #fff5f5;
  color: #dc3545;
}

.bookmarks-footer {
  padding: 8px 16px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  text-align: center;
}

.bookmarks-count {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

/* Scrollbar styling */
.bookmarks-list::-webkit-scrollbar {
  width: 6px;
}

.bookmarks-list::-webkit-scrollbar-track {
  background: #f1f3f4;
}

.bookmarks-list::-webkit-scrollbar-thumb {
  background: #c1c8cd;
  border-radius: 3px;
}

.bookmarks-list::-webkit-scrollbar-thumb:hover {
  background: #a8b2ba;
}

/* Responsive design */
@media (max-width: 768px) {
  .bookmarks-panel {
    max-height: 300px;
  }
  
  .bookmarks-header {
    padding: 10px 12px;
  }
  
  .bookmarks-search {
    padding: 10px 12px;
  }
  
  .bookmark-item {
    padding: 10px 12px;
  }
  
  .bookmark-title {
    font-size: 13px;
  }
  
  .bookmark-url {
    font-size: 11px;
  }
  
  .remove-bookmark {
    opacity: 1; /* Always show on mobile */
  }
}

@media (max-width: 480px) {
  .bookmarks-panel {
    max-height: 250px;
  }
  
  .bookmark-content {
    gap: 8px;
  }
  
  .bookmark-favicon {
    font-size: 14px;
  }
}
