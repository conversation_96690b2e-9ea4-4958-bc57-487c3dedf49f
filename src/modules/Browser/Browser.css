.browser-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.browser-toolbar {
  display: flex;
  padding: 8px;
  background-color: #e0e0e0;
  border-bottom: 1px solid #ccc;
}

.browser-navigation {
  display: flex;
  margin-right: 10px;
}

.nav-button {
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 5px 10px;
  margin-right: 5px;
  cursor: pointer;
  font-size: 16px;
}

.nav-button:hover {
  background-color: #e0e0e0;
}

.url-form {
  display: flex;
  flex: 1;
}

.url-input {
  flex: 1;
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.go-button {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 15px;
  margin-left: 5px;
  cursor: pointer;
}

.go-button:hover {
  background-color: #3367d6;
}

.browser-content {
  flex: 1;
  position: relative;
}

.webview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 10;
}
