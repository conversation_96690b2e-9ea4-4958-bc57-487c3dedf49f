import React, { useState } from "react";
import BrowserToolbar from "./components/BrowserToolbar.js";
import BrowserTabs from "./components/BrowserTabs.js";
import BrowserContent from "./components/BrowserContent.js";
import BookmarksPanel from "./components/BookmarksPanel.js";
import { useBrowser } from "./hooks/useBrowser.js";
import "./Browser.css";

function Browser() {
  const [showBookmarks, setShowBookmarks] = useState(false);
  
  const {
    url,
    currentUrl,
    isLoading,
    error,
    history,
    bookmarks,
    tabs,
    activeTabId,
    webviewRef,
    canGoBack,
    canGoForward,
    setUrl,
    navigateToUrl,
    goBack,
    goForward,
    refresh,
    createNewTab,
    closeTab,
    switchTab,
    addBookmark,
    removeBookmark,
    handleWebviewLoad,
    handleWebviewError
  } = useBrowser();

  const handleToggleBookmarks = () => {
    setShowBookmarks(!showBookmarks);
  };

  const handleNavigateToBookmark = (bookmarkUrl) => {
    navigateToUrl(bookmarkUrl);
    setShowBookmarks(false);
  };

  return (
    <div className="browser-container">
      <BrowserTabs
        tabs={tabs}
        activeTabId={activeTabId}
        onSwitchTab={switchTab}
        onCloseTab={closeTab}
        onCreateTab={createNewTab}
      />
      
      <div className="browser-main">
        <BrowserToolbar
          url={url}
          currentUrl={currentUrl}
          isLoading={isLoading}
          canGoBack={canGoBack}
          canGoForward={canGoForward}
          onUrlChange={setUrl}
          onNavigate={navigateToUrl}
          onBack={goBack}
          onForward={goForward}
          onRefresh={refresh}
          onAddBookmark={addBookmark}
          onToggleBookmarks={handleToggleBookmarks}
        />
        
        <BookmarksPanel
          bookmarks={bookmarks}
          isVisible={showBookmarks}
          onNavigateToBookmark={handleNavigateToBookmark}
          onRemoveBookmark={removeBookmark}
          onClose={() => setShowBookmarks(false)}
        />
        
        <BrowserContent
          currentUrl={currentUrl}
          isLoading={isLoading}
          error={error}
          webviewRef={webviewRef}
          onLoad={handleWebviewLoad}
          onError={handleWebviewError}
        />
      </div>
    </div>
  );
}

export default Browser;
