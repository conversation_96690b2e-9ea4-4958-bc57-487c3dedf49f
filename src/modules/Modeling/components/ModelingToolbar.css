.modeling-toolbar-container {
  background-color: #2a2a2a;
  border-bottom: 2px solid #444;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  overflow-x: auto;
  overflow-y: hidden;
}

.modeling-toolbar-scroll {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
  min-width: max-content;
  white-space: nowrap;
}

.toolbar-section-header {
  display: flex;
  align-items: center;
  padding: 0 8px;
  margin-right: 4px;
}

.toolbar-section-header span {
  font-size: 10px;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.toolbar-separator {
  width: 1px;
  height: 32px;
  background-color: #444;
  margin: 0 8px;
}

.toolbar-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #3a3a3a;
  color: white;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  height: 60px;
  gap: 4px;
}

.toolbar-btn:hover {
  background-color: #4a4a4a;
  border-color: #666;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.toolbar-btn.active {
  background-color: #2ecc71;
  border-color: #27ae60;
  box-shadow: 0 0 15px rgba(46, 204, 113, 0.4);
}

.toolbar-btn:disabled {
  background-color: #2a2a2a;
  border-color: #333;
  color: #666;
  cursor: not-allowed;
  opacity: 0.5;
  transform: none;
  box-shadow: none;
}

.btn-icon {
  font-size: 20px;
  line-height: 1;
  margin-bottom: 2px;
}

.btn-label {
  font-size: 9px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toolbar-toggle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #3a3a3a;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  height: 60px;
  gap: 4px;
  color: white;
}

.toolbar-toggle:hover {
  background-color: #4a4a4a;
  border-color: #666;
}

.toolbar-toggle input[type="checkbox"] {
  display: none;
}

.toolbar-toggle input[type="checkbox"]:checked + .toggle-icon {
  filter: brightness(1.5);
}

.toolbar-toggle input[type="checkbox"]:checked ~ .toggle-label {
  color: #2ecc71;
}

.toggle-icon {
  font-size: 20px;
  line-height: 1;
  margin-bottom: 2px;
  transition: filter 0.2s ease;
}

.toggle-label {
  font-size: 9px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  transition: color 0.2s ease;
}

/* Scrollbar styling */
.modeling-toolbar-container::-webkit-scrollbar {
  height: 6px;
}

.modeling-toolbar-container::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.modeling-toolbar-container::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.modeling-toolbar-container::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .toolbar-btn,
  .toolbar-toggle {
    min-width: 50px;
    height: 50px;
    padding: 6px 8px;
  }
  
  .btn-icon,
  .toggle-icon {
    font-size: 16px;
  }
  
  .btn-label,
  .toggle-label {
    font-size: 8px;
  }
  
  .modeling-toolbar-scroll {
    padding: 6px 8px;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .toolbar-btn,
  .toolbar-toggle {
    min-width: 45px;
    height: 45px;
    padding: 4px 6px;
  }
  
  .btn-icon,
  .toggle-icon {
    font-size: 14px;
  }
  
  .btn-label,
  .toggle-label {
    font-size: 7px;
  }
  
  .toolbar-section-header {
    display: none; /* Hide section headers on very small screens */
  }
}
