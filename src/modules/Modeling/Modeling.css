.modeling-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1e1e1e;
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.modeling-toolbar {
  background-color: #2a2a2a;
  color: white;
  padding: 12px 15px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  border-bottom: 2px solid #444;
  flex-wrap: wrap;
  gap: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.toolbar-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
}

.toolbar-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #aaa;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.toolbar-section > div {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.modeling-toolbar button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.modeling-toolbar button:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.modeling-toolbar button.active {
  background-color: #2ecc71;
  box-shadow: 0 0 10px rgba(46, 204, 113, 0.4);
}

.modeling-toolbar button:disabled {
  background-color: #555;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.modeling-toolbar label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #ccc;
  cursor: pointer;
}

.modeling-toolbar input[type="checkbox"] {
  accent-color: #3498db;
}

.modeling-workspace {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.modeling-sidebar {
  width: 300px;
  background-color: #252525;
  border-right: 2px solid #444;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-tabs {
  display: flex;
  background-color: #333;
  border-bottom: 1px solid #444;
}

.panel-tabs button {
  flex: 1;
  background-color: transparent;
  color: #ccc;
  border: none;
  padding: 12px 8px;
  cursor: pointer;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
}

.panel-tabs button:hover {
  background-color: #3a3a3a;
  color: #fff;
}

.panel-tabs button.active {
  background-color: #3498db;
  color: white;
}

.panel-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.panel-content h4 {
  margin: 0 0 15px 0;
  color: #fff;
  font-size: 14px;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.panel-content h5 {
  margin: 15px 0 10px 0;
  color: #ddd;
  font-size: 12px;
}

.object-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #444;
  border-radius: 4px;
}

.object-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #333;
  transition: background-color 0.2s ease;
}

.object-item:hover {
  background-color: #333;
}

.object-item.selected {
  background-color: #3498db;
  color: white;
}

.object-icon {
  margin-right: 8px;
  font-size: 14px;
}

.object-name {
  flex: 1;
  font-size: 12px;
}

.object-visibility {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 2px;
}

.object-properties {
  margin-top: 15px;
  padding: 12px;
  background-color: #2a2a2a;
  border-radius: 6px;
  border: 1px solid #444;
}

.property-group {
  margin-bottom: 12px;
}

.property-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 11px;
  color: #ccc;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vector-input {
  display: flex;
  gap: 4px;
}

.vector-input input {
  flex: 1;
  background-color: #333;
  border: 1px solid #555;
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 11px;
}

.vector-input input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.material-editor {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.material-type select {
  width: 100%;
  background-color: #333;
  border: 1px solid #555;
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.material-properties {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.material-properties label {
  font-size: 11px;
  color: #ccc;
  margin-bottom: 4px;
}

.material-properties input[type="color"] {
  width: 100%;
  height: 40px;
  border: 1px solid #555;
  border-radius: 4px;
  background-color: #333;
  cursor: pointer;
}

.material-properties input[type="range"] {
  width: 100%;
  accent-color: #3498db;
}

.light-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.light-item {
  padding: 12px;
  background-color: #2a2a2a;
  border-radius: 6px;
  border: 1px solid #444;
}

.light-item h5 {
  margin: 0 0 10px 0;
  color: #fff;
}

.light-item label {
  display: block;
  margin-bottom: 6px;
  font-size: 11px;
  color: #ccc;
}

.light-item input[type="range"] {
  width: 100%;
  accent-color: #f39c12;
}

.animation-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.animation-controls button {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.animation-controls button:hover {
  background-color: #c0392b;
}

.modeling-canvas {
  flex: 1;
  overflow: hidden;
  position: relative;
}
