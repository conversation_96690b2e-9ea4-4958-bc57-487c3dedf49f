import { useRef, useState, useEffect, useCallback } from "react";
import * as THREE from "three";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";
import { TransformControls } from "three/addons/controls/TransformControls.js";
import "./Modeling.css";

function Modeling() {
  const containerRef = useRef(null);
  const [scene, setScene] = useState(null);
  const [transformControls, setTransformControls] = useState(null);
  const [selectedObject, setSelectedObject] = useState(null);
  const [mode, setMode] = useState("translate"); // translate, rotate, scale
  const [objects, setObjects] = useState([]);
  const [lights, setLights] = useState([]);
  const [activePanel, setActivePanel] = useState("objects"); // objects, materials, lighting, animation
  const [snapToGrid, setSnapToGrid] = useState(false);
  const renderSettings = {
    shadows: true,
    antialias: true,
    physicallyCorrectLights: true,
    toneMapping: THREE.ACESFilmicToneMapping,
    exposure: 1.0
  };

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    // Create scene
    const newScene = new THREE.Scene();
    newScene.background = new THREE.Color(0x2a2a2a);
    newScene.fog = new THREE.Fog(0x2a2a2a, 50, 200);

    // Create camera
    const newCamera = new THREE.PerspectiveCamera(
      45,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      2000
    );
    newCamera.position.set(10, 10, 10);
    newCamera.lookAt(0, 0, 0);

    // Create renderer with advanced settings
    const newRenderer = new THREE.WebGLRenderer({
      antialias: renderSettings.antialias,
      powerPreference: "high-performance"
    });
    newRenderer.setSize(
      containerRef.current.clientWidth,
      containerRef.current.clientHeight
    );
    newRenderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    newRenderer.shadowMap.enabled = renderSettings.shadows;
    newRenderer.shadowMap.type = THREE.PCFSoftShadowMap;
    newRenderer.physicallyCorrectLights = renderSettings.physicallyCorrectLights;
    newRenderer.toneMapping = renderSettings.toneMapping;
    newRenderer.toneMappingExposure = renderSettings.exposure;
    newRenderer.outputEncoding = THREE.sRGBEncoding;
    containerRef.current.appendChild(newRenderer.domElement);

    // Create enhanced grid and axes
    const gridHelper = new THREE.GridHelper(50, 50, 0x444444, 0x222222);
    newScene.add(gridHelper);

    const axesHelper = new THREE.AxesHelper(10);
    newScene.add(axesHelper);

    // Enhanced lighting setup
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    newScene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    directionalLight.shadow.camera.left = -50;
    directionalLight.shadow.camera.right = 50;
    directionalLight.shadow.camera.top = 50;
    directionalLight.shadow.camera.bottom = -50;
    newScene.add(directionalLight);

    // Add rim light
    const rimLight = new THREE.DirectionalLight(0x4080ff, 0.5);
    rimLight.position.set(-10, 5, -10);
    newScene.add(rimLight);

    // Add fill light
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
    fillLight.position.set(5, -5, 10);
    newScene.add(fillLight);

    // Create enhanced orbit controls
    const newControls = new OrbitControls(newCamera, newRenderer.domElement);
    newControls.enableDamping = true;
    newControls.dampingFactor = 0.05;
    newControls.enableZoom = true;
    newControls.enablePan = true;
    newControls.enableRotate = true;
    newControls.maxDistance = 500;
    newControls.minDistance = 1;
    newControls.maxPolarAngle = Math.PI;

    // Create transform controls
    const newTransformControls = new TransformControls(
      newCamera,
      newRenderer.domElement
    );
    newTransformControls.addEventListener("dragging-changed", (event) => {
      newControls.enabled = !event.value;
    });
    newTransformControls.setSpace("local");
    newTransformControls.setSize(0.8);
    newScene.add(newTransformControls);

    // Initialize object arrays
    const sceneObjects = [];
    const sceneLights = [ambientLight, directionalLight, rimLight, fillLight];

    // Set state
    setScene(newScene);
    setTransformControls(newTransformControls);
    setObjects(sceneObjects);
    setLights(sceneLights);

    // Enhanced animation loop with stats
    let animationId;
    const animate = () => {
      animationId = requestAnimationFrame(animate);

      newControls.update();

      // Update any animated objects
      sceneObjects.forEach(obj => {
        if (obj.userData.animated) {
          obj.rotation.y += 0.01;
        }
      });

      newRenderer.render(newScene, newCamera);
    };
    animate();

    // Handle window resize
    const handleResize = () => {
      if (!containerRef.current) return;

      newCamera.aspect =
        containerRef.current.clientWidth / containerRef.current.clientHeight;
      newCamera.updateProjectionMatrix();
      newRenderer.setSize(
        containerRef.current.clientWidth,
        containerRef.current.clientHeight
      );
    };

    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => {
      window.removeEventListener("resize", handleResize);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      if (containerRef.current && newRenderer.domElement) {
        containerRef.current.removeChild(newRenderer.domElement);
      }
    };
  }, []);

  // Update transform controls mode
  useEffect(() => {
    if (transformControls && selectedObject) {
      transformControls.setMode(mode);
    }
  }, [mode, transformControls, selectedObject]);

  // Enhanced object creation with advanced materials
  const createMaterial = (type = "standard", options = {}) => {
    const defaultOptions = {
      color: 0x888888,
      metalness: 0.0,
      roughness: 0.5,
      transparent: false,
      opacity: 1.0,
      wireframe: false,
      ...options
    };

    switch (type) {
      case "basic":
        return new THREE.MeshBasicMaterial(defaultOptions);
      case "lambert":
        return new THREE.MeshLambertMaterial(defaultOptions);
      case "phong":
        return new THREE.MeshPhongMaterial(defaultOptions);
      case "physical":
        return new THREE.MeshPhysicalMaterial(defaultOptions);
      case "standard":
      default:
        return new THREE.MeshStandardMaterial(defaultOptions);
    }
  };

  const addObject = useCallback((geometryType, materialOptions = {}) => {
    if (!scene) return;

    let geometry;
    const material = createMaterial("standard", materialOptions);

    switch (geometryType) {
      case "cube":
        geometry = new THREE.BoxGeometry(2, 2, 2, 2, 2, 2);
        break;
      case "sphere":
        geometry = new THREE.SphereGeometry(1, 32, 32);
        break;
      case "cylinder":
        geometry = new THREE.CylinderGeometry(1, 1, 2, 32);
        break;
      case "cone":
        geometry = new THREE.ConeGeometry(1, 2, 32);
        break;
      case "torus":
        geometry = new THREE.TorusGeometry(1, 0.4, 16, 100);
        break;
      case "plane":
        geometry = new THREE.PlaneGeometry(2, 2, 10, 10);
        break;
      case "icosahedron":
        geometry = new THREE.IcosahedronGeometry(1, 2);
        break;
      case "dodecahedron":
        geometry = new THREE.DodecahedronGeometry(1, 0);
        break;
      case "octahedron":
        geometry = new THREE.OctahedronGeometry(1, 0);
        break;
      case "tetrahedron":
        geometry = new THREE.TetrahedronGeometry(1, 0);
        break;
      default:
        geometry = new THREE.BoxGeometry(2, 2, 2);
    }

    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(0, 1, 0);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    mesh.name = `${geometryType}_${Date.now()}`;
    mesh.userData = {
      type: geometryType,
      created: Date.now(),
      animated: false,
      materialType: "standard"
    };

    scene.add(mesh);
    setObjects(prev => [...prev, mesh]);
    selectObject(mesh);
  }, [scene]);

  // Primitive creation functions
  const addCube = () => addObject("cube", { color: 0x3498db });
  const addSphere = () => addObject("sphere", { color: 0xe74c3c });
  const addCylinder = () => addObject("cylinder", { color: 0x2ecc71 });
  const addCone = () => addObject("cone", { color: 0xf39c12 });
  const addTorus = () => addObject("torus", { color: 0x9b59b6 });
  const addPlane = () => addObject("plane", { color: 0x95a5a6 });
  const addIcosahedron = () => addObject("icosahedron", { color: 0x1abc9c });
  const addDodecahedron = () => addObject("dodecahedron", { color: 0xe67e22 });
  const addOctahedron = () => addObject("octahedron", { color: 0x34495e });
  const addTetrahedron = () => addObject("tetrahedron", { color: 0x8e44ad });

  // Advanced object manipulation
  const selectObject = useCallback((object) => {
    if (!transformControls) return;

    setSelectedObject(object);
    transformControls.attach(object);
  }, [transformControls]);

  const deleteSelected = useCallback(() => {
    if (!scene || !selectedObject) return;

    scene.remove(selectedObject);
    transformControls.detach();
    setObjects(prev => prev.filter(obj => obj !== selectedObject));
    setSelectedObject(null);
  }, [scene, selectedObject, transformControls]);

  const duplicateSelected = useCallback(() => {
    if (!scene || !selectedObject) return;

    const cloned = selectedObject.clone();
    cloned.position.x += 2;
    cloned.name = `${selectedObject.userData.type}_${Date.now()}`;
    cloned.userData = { ...selectedObject.userData, created: Date.now() };

    scene.add(cloned);
    setObjects(prev => [...prev, cloned]);
    selectObject(cloned);
  }, [scene, selectedObject, selectObject]);

  // Mesh operations
  const subdivideSelected = useCallback(() => {
    if (!selectedObject || !selectedObject.geometry) return;

    // Simple subdivision by increasing geometry segments
    const geo = selectedObject.geometry;
    if (geo.parameters) {
      const params = geo.parameters;
      let newGeo;

      switch (selectedObject.userData.type) {
        case "sphere":
          newGeo = new THREE.SphereGeometry(
            params.radius,
            Math.min(params.widthSegments * 2, 64),
            Math.min(params.heightSegments * 2, 64)
          );
          break;
        case "cylinder":
          newGeo = new THREE.CylinderGeometry(
            params.radiusTop,
            params.radiusBottom,
            params.height,
            Math.min(params.radialSegments * 2, 64)
          );
          break;
        case "cube":
          newGeo = new THREE.BoxGeometry(
            params.width,
            params.height,
            params.depth,
            Math.min((params.widthSegments || 1) * 2, 32),
            Math.min((params.heightSegments || 1) * 2, 32),
            Math.min((params.depthSegments || 1) * 2, 32)
          );
          break;
        default:
          return;
      }

      selectedObject.geometry.dispose();
      selectedObject.geometry = newGeo;
    }
  }, [selectedObject]);

  // Material operations
  const changeMaterial = useCallback((materialType, options = {}) => {
    if (!selectedObject) return;

    const oldMaterial = selectedObject.material;
    const newMaterial = createMaterial(materialType, {
      color: oldMaterial.color,
      ...options
    });

    selectedObject.material = newMaterial;
    selectedObject.userData.materialType = materialType;
    oldMaterial.dispose();
  }, [selectedObject]);

  // View mode controls
  const toggleWireframe = useCallback(() => {
    if (!selectedObject) return;
    selectedObject.material.wireframe = !selectedObject.material.wireframe;
  }, [selectedObject]);



  // Animation controls
  const toggleAnimation = useCallback(() => {
    if (!selectedObject) return;
    selectedObject.userData.animated = !selectedObject.userData.animated;
  }, [selectedObject]);

  // Define toolbar items with icons
  const toolbarItems = [
    // Add Objects
    { type: 'section', title: 'Add Objects' },
    { type: 'button', icon: '🧊', label: 'Cube', action: addCube, tooltip: 'Add Cube' },
    { type: 'button', icon: '⚪', label: 'Sphere', action: addSphere, tooltip: 'Add Sphere' },
    { type: 'button', icon: '🥫', label: 'Cylinder', action: addCylinder, tooltip: 'Add Cylinder' },
    { type: 'button', icon: '🔺', label: 'Cone', action: addCone, tooltip: 'Add Cone' },
    { type: 'button', icon: '🍩', label: 'Torus', action: addTorus, tooltip: 'Add Torus' },
    { type: 'button', icon: '⬜', label: 'Plane', action: addPlane, tooltip: 'Add Plane' },
    { type: 'button', icon: '💎', label: 'Icosahedron', action: addIcosahedron, tooltip: 'Add Icosahedron' },
    { type: 'button', icon: '🎲', label: 'Dodecahedron', action: addDodecahedron, tooltip: 'Add Dodecahedron' },
    { type: 'button', icon: '🔷', label: 'Octahedron', action: addOctahedron, tooltip: 'Add Octahedron' },
    { type: 'button', icon: '🔻', label: 'Tetrahedron', action: addTetrahedron, tooltip: 'Add Tetrahedron' },

    { type: 'separator' },

    // Transform
    { type: 'section', title: 'Transform' },
    { type: 'button', icon: '↔️', label: 'Move', action: () => setMode("translate"), active: mode === "translate", tooltip: 'Move (G)' },
    { type: 'button', icon: '🔄', label: 'Rotate', action: () => setMode("rotate"), active: mode === "rotate", tooltip: 'Rotate (R)' },
    { type: 'button', icon: '📏', label: 'Scale', action: () => setMode("scale"), active: mode === "scale", tooltip: 'Scale (S)' },

    { type: 'separator' },

    // Edit
    { type: 'section', title: 'Edit' },
    { type: 'button', icon: '🗑️', label: 'Delete', action: deleteSelected, disabled: !selectedObject, tooltip: 'Delete (X)' },
    { type: 'button', icon: '📋', label: 'Duplicate', action: duplicateSelected, disabled: !selectedObject, tooltip: 'Duplicate (Shift+D)' },
    { type: 'button', icon: '🔀', label: 'Subdivide', action: subdivideSelected, disabled: !selectedObject, tooltip: 'Subdivide' },

    { type: 'separator' },

    // View
    { type: 'section', title: 'View' },
    { type: 'button', icon: '🕸️', label: 'Wireframe', action: toggleWireframe, disabled: !selectedObject, tooltip: 'Toggle Wireframe' },
    { type: 'toggle', icon: '🧲', label: 'Snap to Grid', checked: snapToGrid, action: setSnapToGrid, tooltip: 'Snap to Grid' },
  ];

  return (
    <div className="modeling-container">
      {/* Scrolling Toolbar */}
      <div className="modeling-toolbar-container">
        <div className="modeling-toolbar-scroll">
          {toolbarItems.map((item, index) => {
            if (item.type === 'section') {
              return (
                <div key={index} className="toolbar-section-header">
                  <span>{item.title}</span>
                </div>
              );
            } else if (item.type === 'separator') {
              return <div key={index} className="toolbar-separator" />;
            } else if (item.type === 'button') {
              return (
                <button
                  key={index}
                  className={`toolbar-btn ${item.active ? 'active' : ''}`}
                  onClick={item.action}
                  disabled={item.disabled}
                  title={item.tooltip}
                >
                  <span className="btn-icon">{item.icon}</span>
                  <span className="btn-label">{item.label}</span>
                </button>
              );
            } else if (item.type === 'toggle') {
              return (
                <label key={index} className="toolbar-toggle" title={item.tooltip}>
                  <input
                    type="checkbox"
                    checked={item.checked}
                    onChange={(e) => item.action(e.target.checked)}
                  />
                  <span className="toggle-icon">{item.icon}</span>
                  <span className="toggle-label">{item.label}</span>
                </label>
              );
            }
            return null;
          })}
        </div>
      </div>

      <div className="modeling-workspace">
        {/* Side Panel */}
        <div className="modeling-sidebar">
          <div className="panel-tabs">
            <button
              className={activePanel === "objects" ? "active" : ""}
              onClick={() => setActivePanel("objects")}
            >
              Objects
            </button>
            <button
              className={activePanel === "materials" ? "active" : ""}
              onClick={() => setActivePanel("materials")}
            >
              Materials
            </button>
            <button
              className={activePanel === "lighting" ? "active" : ""}
              onClick={() => setActivePanel("lighting")}
            >
              Lighting
            </button>
            <button
              className={activePanel === "animation" ? "active" : ""}
              onClick={() => setActivePanel("animation")}
            >
              Animation
            </button>
          </div>

          <div className="panel-content">
            {activePanel === "objects" && (
              <div className="objects-panel">
                <h4>Scene Objects ({objects.length})</h4>
                <div className="object-list">
                  {objects.map((obj) => (
                    <div
                      key={obj.uuid}
                      className={`object-item ${selectedObject === obj ? "selected" : ""}`}
                      onClick={() => selectObject(obj)}
                    >
                      <span className="object-icon">📦</span>
                      <span className="object-name">{obj.name}</span>
                      <button
                        className="object-visibility"
                        onClick={(e) => {
                          e.stopPropagation();
                          obj.visible = !obj.visible;
                        }}
                      >
                        {obj.visible ? "👁" : "🚫"}
                      </button>
                    </div>
                  ))}
                </div>

                {selectedObject && (
                  <div className="object-properties">
                    <h5>Properties: {selectedObject.name}</h5>
                    <div className="property-group">
                      <label>Position</label>
                      <div className="vector-input">
                        <input
                          type="number"
                          step="0.1"
                          value={selectedObject.position.x.toFixed(2)}
                          onChange={(e) => {
                            selectedObject.position.x = parseFloat(e.target.value);
                          }}
                        />
                        <input
                          type="number"
                          step="0.1"
                          value={selectedObject.position.y.toFixed(2)}
                          onChange={(e) => {
                            selectedObject.position.y = parseFloat(e.target.value);
                          }}
                        />
                        <input
                          type="number"
                          step="0.1"
                          value={selectedObject.position.z.toFixed(2)}
                          onChange={(e) => {
                            selectedObject.position.z = parseFloat(e.target.value);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activePanel === "materials" && (
              <div className="materials-panel">
                <h4>Material Editor</h4>
                {selectedObject && (
                  <div className="material-editor">
                    <div className="material-type">
                      <label>Material Type:</label>
                      <select
                        value={selectedObject.userData.materialType || "standard"}
                        onChange={(e) => changeMaterial(e.target.value)}
                      >
                        <option value="basic">Basic</option>
                        <option value="lambert">Lambert</option>
                        <option value="phong">Phong</option>
                        <option value="standard">Standard</option>
                        <option value="physical">Physical</option>
                      </select>
                    </div>

                    <div className="material-properties">
                      <label>Color:</label>
                      <input
                        type="color"
                        value={`#${selectedObject.material.color.getHexString()}`}
                        onChange={(e) => {
                          selectedObject.material.color.setHex(e.target.value.replace('#', '0x'));
                        }}
                      />

                      <label>Metalness:</label>
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.01"
                        value={selectedObject.material.metalness || 0}
                        onChange={(e) => {
                          if (selectedObject.material.metalness !== undefined) {
                            selectedObject.material.metalness = parseFloat(e.target.value);
                          }
                        }}
                      />

                      <label>Roughness:</label>
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.01"
                        value={selectedObject.material.roughness || 0.5}
                        onChange={(e) => {
                          if (selectedObject.material.roughness !== undefined) {
                            selectedObject.material.roughness = parseFloat(e.target.value);
                          }
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            {activePanel === "lighting" && (
              <div className="lighting-panel">
                <h4>Lighting Setup</h4>
                <div className="light-controls">
                  <div className="light-item">
                    <h5>Ambient Light</h5>
                    <label>Intensity:</label>
                    <input
                      type="range"
                      min="0"
                      max="2"
                      step="0.1"
                      defaultValue="0.3"
                      onChange={(e) => {
                        if (lights[0]) lights[0].intensity = parseFloat(e.target.value);
                      }}
                    />
                  </div>

                  <div className="light-item">
                    <h5>Directional Light</h5>
                    <label>Intensity:</label>
                    <input
                      type="range"
                      min="0"
                      max="3"
                      step="0.1"
                      defaultValue="1.0"
                      onChange={(e) => {
                        if (lights[1]) lights[1].intensity = parseFloat(e.target.value);
                      }}
                    />
                  </div>
                </div>
              </div>
            )}

            {activePanel === "animation" && (
              <div className="animation-panel">
                <h4>Animation Controls</h4>
                {selectedObject && (
                  <div className="animation-controls">
                    <button onClick={toggleAnimation}>
                      {selectedObject.userData.animated ? "Stop Animation" : "Start Animation"}
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Main Canvas */}
        <div className="modeling-canvas" ref={containerRef}></div>
      </div>
    </div>
  );
}

export default Modeling;
