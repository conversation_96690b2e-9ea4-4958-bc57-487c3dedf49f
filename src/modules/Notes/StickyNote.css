.sticky-note {
  position: absolute;
  width: 200px;
  min-height: 200px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.note-header {
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  cursor: move;
  background-color: rgba(0, 0, 0, 0.1);
}

.drag-handle {
  width: 20px;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.delete-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.5);
}

.delete-btn:hover {
  color: rgba(0, 0, 0, 0.8);
}

.note-content {
  flex: 1;
  padding: 10px;
  border: none;
  resize: none;
  background-color: transparent;
  font-family: inherit;
  font-size: 14px;
  outline: none;
}
