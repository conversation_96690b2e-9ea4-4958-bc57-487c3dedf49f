.notes-container {
  display: flex;
  height: 100%;
  background-color: #fff;
}

.notes-container.right-panel-mode {
  flex-direction: column;
}

.notes-container.right-panel-mode .notes-sidebar {
  width: 100%;
  max-height: 35%;
  min-height: 200px;
  border-right: none;
  border-bottom: 1px solid #e9ecef;
}

.notes-container.right-panel-mode .note-editor-container {
  flex: 1;
  min-height: 0;
}

.notes-sidebar {
  width: 300px;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.note-editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Legacy styles removed - now handled by individual components */
