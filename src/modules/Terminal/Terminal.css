.terminal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1e1e1e;
  overflow: hidden;
}

.terminal-header {
  background-color: #333;
  color: white;
  padding: 8px 15px;
  font-family: monospace;
  text-align: left;
}

.terminal {
  flex: 1;
  padding: 0;
  width: 100%;
  height: calc(100% - 36px);
  position: relative;
  text-align: left;
}

/* Terminal tabs */
.terminal-tabs {
  display: flex;
  background-color: #252526;
  border-bottom: 1px solid #3c3c3c;
  height: 36px;
  overflow-x: auto;
  white-space: nowrap;
}

.terminal-tab {
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 36px;
  background-color: #2d2d2d;
  color: #cccccc;
  border-right: 1px solid #3c3c3c;
  cursor: pointer;
  user-select: none;
}

.terminal-tab.active {
  background-color: #1e1e1e;
  color: #ffffff;
}

.terminal-tab span {
  margin-right: 8px;
}

.close-tab-btn {
  background: none;
  border: none;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  padding: 0 4px;
}

.close-tab-btn:hover {
  color: #fff;
}

.add-tab-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #2d2d2d;
  color: #cccccc;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

.add-tab-btn:hover {
  background-color: #3e3e3e;
}

/* Terminal content */
.terminal-content {
  flex: 1;
  position: relative;
  height: calc(100% - 36px);
}

.terminal-instance {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  overflow: hidden;
}

.terminal-instance.active {
  display: block;
}

/* Simple terminal styles */
.simple-terminal {
  flex: 1;
  background-color: #1e1e1e;
  color: #f0f0f0;
  font-family: "Courier New", monospace;
  padding: 10px;
  overflow-y: auto;
  height: calc(100% - 36px);
  text-align: left;
  margin: 0;
  display: block;
}

.terminal-history {
  margin-bottom: 10px;
  text-align: left;
  width: 100%;
}

.terminal-line {
  white-space: pre-wrap;
  line-height: 1.5;
  text-align: left;
  margin: 0;
  padding: 0;
}

.terminal-line.command {
  color: #3498db;
  text-align: left;
}

.terminal-input-line {
  display: flex;
  align-items: center;
  text-align: left;
  width: 100%;
  justify-content: flex-start;
}

.prompt {
  color: #3498db;
  margin-right: 5px;
  text-align: left;
}

.terminal-input {
  background: transparent;
  border: none;
  color: #f0f0f0;
  font-family: "Courier New", monospace;
  font-size: inherit;
  flex: 1;
  outline: none;
  text-align: left;
}

/* Override xterm.js styles for better fit */
.xterm {
  height: 100%;
}

.xterm-viewport {
  overflow-y: auto !important;
}

.xterm-screen {
  width: 100%;
  height: 100%;
}

/* Ensure terminal text is left-aligned */
.xterm-rows {
  text-align: left;
}

/* Remove any centering styles that might be applied */
.terminal * {
  justify-content: flex-start;
  align-items: flex-start;
  text-align: left;
}

/* Ensure cursor is properly aligned */
.xterm-cursor-layer {
  left: 0;
}
