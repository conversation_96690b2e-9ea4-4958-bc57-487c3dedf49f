.terminal-display {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
  cursor: text;
}

.terminal-history {
  min-height: 100%;
}

.terminal-line {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
  padding: 2px 0;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.terminal-line.command {
  color: #569cd6;
  font-weight: 500;
}

.terminal-line.error {
  color: #f44747;
}

.terminal-line.output {
  color: #d4d4d4;
}

.line-icon {
  display: inline-block;
  width: 16px;
  margin-right: 8px;
  font-size: 12px;
  opacity: 0.7;
  flex-shrink: 0;
}

.line-content {
  flex: 1;
  min-width: 0;
}

.prompt {
  color: #4ec9b0;
  font-weight: bold;
  margin-right: 4px;
}

.command-text {
  color: #569cd6;
  font-weight: 500;
}

.output-text {
  color: #d4d4d4;
}

.output-text.error {
  color: #f44747;
}

.output-text.success {
  color: #4ec9b0;
}

.output-text.warning {
  color: #ffcc02;
}

/* Scrollbar styling */
.terminal-display::-webkit-scrollbar {
  width: 8px;
}

.terminal-display::-webkit-scrollbar-track {
  background: #2d2d30;
}

.terminal-display::-webkit-scrollbar-thumb {
  background: #464647;
  border-radius: 4px;
}

.terminal-display::-webkit-scrollbar-thumb:hover {
  background: #5a5a5c;
}

/* Selection styling */
.terminal-display ::selection {
  background-color: rgba(86, 156, 214, 0.3);
}

/* Hover effects */
.terminal-line:hover {
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: 2px;
}

/* Animation for new lines */
.terminal-line {
  animation: fadeIn 0.2s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .terminal-display {
    padding: 12px;
    font-size: 13px;
  }
  
  .line-icon {
    width: 14px;
    margin-right: 6px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .terminal-display {
    padding: 8px;
    font-size: 12px;
  }
  
  .line-icon {
    display: none; /* Hide icons on very small screens */
  }
}
