.terminal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3c3c3c;
  color: #cccccc;
  font-size: 12px;
  position: relative;
}

.terminal-header.light {
  background-color: #f3f3f3;
  border-bottom-color: #e1e1e1;
  color: #333333;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.terminal-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
}

.title-icon {
  font-size: 14px;
}

.title-text {
  font-size: 13px;
}

.terminal-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #6c757d;
}

.status-indicator.active {
  background-color: #28a745;
  box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}

.status-text {
  font-size: 11px;
  color: #9d9d9d;
}

.header-center {
  display: flex;
  align-items: center;
}

.current-time {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 11px;
  color: #9d9d9d;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
}

.header-right {
  display: flex;
  align-items: center;
}

.terminal-controls {
  display: flex;
  gap: 4px;
}

.control-btn {
  background: none;
  border: 1px solid transparent;
  border-radius: 4px;
  padding: 4px 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  color: #cccccc;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.control-btn:active {
  transform: scale(0.95);
}

.terminal-header.light .control-btn {
  color: #666666;
}

.terminal-header.light .control-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.2);
}

.stats-panel {
  position: absolute;
  top: 100%;
  right: 16px;
  background-color: #252526;
  border: 1px solid #3c3c3c;
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 100;
  min-width: 200px;
  animation: slideDown 0.2s ease-out;
}

.terminal-header.light .stats-panel {
  background-color: #ffffff;
  border-color: #e1e1e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label {
  font-size: 10px;
  color: #9d9d9d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
  color: #cccccc;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.terminal-header.light .stat-value {
  color: #333333;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .terminal-header {
    padding: 6px 12px;
    font-size: 11px;
  }
  
  .header-left {
    gap: 12px;
  }
  
  .title-text {
    font-size: 12px;
  }
  
  .current-time {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  .terminal-controls {
    gap: 2px;
  }
  
  .control-btn {
    padding: 3px 5px;
    font-size: 11px;
  }
  
  .stats-panel {
    right: 12px;
    padding: 10px;
    min-width: 180px;
  }
}

@media (max-width: 480px) {
  .terminal-header {
    padding: 4px 8px;
  }
  
  .header-center {
    display: none; /* Hide time on very small screens */
  }
  
  .terminal-status {
    display: none; /* Hide status on very small screens */
  }
  
  .stats-panel {
    right: 8px;
    left: 8px;
    min-width: auto;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .terminal-header {
    border-bottom-width: 2px;
  }
  
  .control-btn:hover {
    border-color: currentColor;
  }
  
  .stats-panel {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .control-btn,
  .stats-panel {
    transition: none;
  }
  
  .stats-panel {
    animation: none;
  }
  
  .control-btn:active {
    transform: none;
  }
}
