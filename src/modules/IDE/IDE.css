.ide-container {
  display: flex;
  height: 100%;
}

.ide-sidebar {
  width: 200px;
  background-color: #252526;
  color: #fff;
  display: flex;
  flex-direction: column;
}

.ide-sidebar-header {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333;
}

.new-file-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
}

.file-list {
  flex: 1;
  overflow-y: auto;
}

.file-item {
  padding: 8px 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333;
}

.file-item:hover {
  background-color: #2d2d2d;
}

.file-item.active {
  background-color: #37373d;
}

.delete-file {
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 16px;
  cursor: pointer;
  visibility: hidden;
}

.file-item:hover .delete-file {
  visibility: visible;
}

.ide-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

.editor-toolbar {
  background-color: #333;
  color: #fff;
  padding: 8px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.save-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.code-editor {
  flex: 1;
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 10px;
  border: none;
  resize: none;
  tab-size: 2;
}
