.App {
  text-align: center;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.app-container {
  display: flex;
  height: 100%;
  width: 100%;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.notes-panel-right {
  background-color: #f8f9fa;
  border-left: 2px solid #e9ecef;
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 250px;
  max-width: 600px;
}

.resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #dee2e6;
  cursor: col-resize;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background-color: #6c757d;
}

.notes-panel-header {
  background-color: #e9ecef;
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notes-panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #495057;
}

.notes-panel-controls {
  display: flex;
  gap: 8px;
}

.notes-panel-controls button {
  background: none;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.notes-panel-controls button:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
  color: #495057;
}

.notes-panel-content {
  flex: 1;
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}
