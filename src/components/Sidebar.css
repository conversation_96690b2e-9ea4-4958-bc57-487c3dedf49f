.sidebar {
  width: 220px;
  background-color: #2c3e50;
  color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #34495e;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-modules {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.sidebar-module {
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  min-height: 48px;
  box-sizing: border-box;
}

.sidebar-module:hover {
  background-color: #34495e;
}

.sidebar-module.active {
  background-color: #3498db;
}

.sidebar-module.docked-right {
  background-color: #27ae60;
}

.sidebar-module.docked-right:hover {
  background-color: #2ecc71;
}

.module-name {
  flex: 1;
  text-align: left;
}

.notes-controls {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  align-items: center;
  height: 24px;
}

.sidebar-module:hover .notes-controls {
  opacity: 1;
}

.position-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 3px;
  padding: 2px 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

.position-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.position-btn.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid #34495e;
}

.show-notes-btn {
  width: 100%;
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.show-notes-btn:hover {
  background-color: #2ecc71;
}
